@echo off
echo ========================================
echo WIP Automation Script
echo ========================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python and try again.
    pause
    exit /b 1
)

echo Python found. Starting WIP automation...
echo.

REM Run the main script
python fixed-wip-automation-script.py

echo.
echo ========================================
echo WIP automation completed!
echo.
echo Output file: JuneWIP_531.xlsx
echo.
echo Press any key to run validation...
pause >nul

REM Run validation
echo.
echo Running output validation...
python validate_output.py

echo.
echo ========================================
echo All done! Check the output files.
echo ========================================
pause
