import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Division cost percentages from the formulas
DIVISION_COST_PERCENTAGES = {
    'CON': 0.29,
    'ENV': 0.45,
    'MIT': 0.09,
    'MLD': 0.07,
    'REM': 0.57,
    'REP': 0.48,
    'RFG': 0.57,
    'TMP': 0.33,
    'TRM': 0.16,
    'WTR': 0.24
}

def read_qb_report(filename, report_type):
    """Read QuickBooks reports and extract the relevant data"""
    print(f"   Reading {filename}...")
    df = pd.read_excel(filename, engine='openpyxl')

    # QuickBooks reports typically have a few header rows before the actual data
    # Look for the row containing column headers like "Customer", "Income", etc.
    header_row = None
    for idx in range(min(10, len(df))):
        row_values = df.iloc[idx].astype(str).tolist()
        print(f"     Row {idx}: {row_values[:3]}...")  # Debug output
        if any('Customer' in val for val in row_values):
            header_row = idx
            print(f"     Found header row at index {idx}")
            break

    if header_row is not None:
        # Get the header row values first
        header_values = df.iloc[header_row].astype(str).tolist()
        print(f"     Header values: {header_values}")

        # Read again with the correct header
        df = pd.read_excel(filename, header=header_row, engine='openpyxl')

        # Manually set column names from the header row if needed
        if 'Unnamed' in str(df.columns[0]):
            df.columns = header_values[:len(df.columns)]

        # Clean column names
        df.columns = [str(col).strip() for col in df.columns]
        print(f"     Columns after processing: {list(df.columns)}")

        # Remove total rows and empty rows
        if 'Customer' in df.columns:
            df = df[df['Customer'].notna()]
            df = df[~df['Customer'].str.contains('Total for', na=False)]
            print(f"     Final dataframe shape: {df.shape}")
        else:
            print(f"     ERROR: 'Customer' column not found in processed dataframe")
    else:
        print(f"     ERROR: Could not find header row with 'Customer' column")

    return df

def extract_job_number(customer_str):
    """Extract job number from customer string"""
    if pd.isna(customer_str):
        return None
    customer_str = str(customer_str)
    # Look for pattern like XX-XXXX-XXX
    import re
    match = re.search(r'\d{2}-\d{4}-[A-Z]{3}', customer_str)
    if match:
        return match.group()
    return None

def process_production_list(filename):
    """Process the production list to get jobs with expenses in the month"""
    df = read_qb_report(filename, 'production')
    
    jobs = []
    if 'Customer' in df.columns:
        # Extract job numbers from customer column
        for customer in df['Customer'].unique():
            if pd.notna(customer):
                job = extract_job_number(str(customer))
                if job:
                    # Check if this customer has non-zero income or expenses
                    customer_data = df[df['Customer'] == customer]
                    if 'Income' in df.columns:
                        income = customer_data['Income'].sum()
                    else:
                        income = 0
                    if 'Expenses' in df.columns:
                        expenses = customer_data['Expenses'].sum()
                    else:
                        expenses = 0
                    
                    # Include if there's any activity
                    if income != 0 or expenses != 0:
                        jobs.append(job)
    
    return list(set(jobs))  # Remove duplicates

def process_invoice_list(filename):
    """Process the invoice list to get jobs invoiced in the month"""
    df = pd.read_excel(filename, engine='openpyxl')
    
    # Find header row containing "Date", "Transaction type", etc.
    header_row = None
    for idx in range(min(10, len(df))):
        row_values = df.iloc[idx].astype(str).tolist()
        if any('Date' in val for val in row_values) and any('Name' in val for val in row_values):
            header_row = idx
            break
    
    if header_row is not None:
        # Read again with the correct header
        df = pd.read_excel(filename, header=header_row, engine='openpyxl')
        df.columns = [str(col).strip() for col in df.columns]
        
        # Extract job numbers from the Name column
        jobs = []
        if 'Name' in df.columns:
            for name in df['Name'].unique():
                if pd.notna(name):
                    job = extract_job_number(str(name))
                    if job:
                        jobs.append(job)
        
        return list(set(jobs))  # Remove duplicates
    
    return []

def process_invoiced_amounts(filename):
    """Process invoiced amount data to get total invoiced per job"""
    df = read_qb_report(filename, 'invoiced_amounts')
    
    invoiced = {}
    if 'Customer' in df.columns and 'Income' in df.columns:
        for _, row in df.iterrows():
            job = extract_job_number(str(row['Customer']))
            if job:
                income = row['Income'] if pd.notna(row['Income']) else 0
                if job in invoiced:
                    invoiced[job] += income
                else:
                    invoiced[job] = income
    
    return invoiced

def process_actual_costs(filename):
    """Process actual cost data to get total costs per job"""
    df = read_qb_report(filename, 'actual_costs')
    
    costs = {}
    if 'Customer' in df.columns and 'Expenses' in df.columns:
        for _, row in df.iterrows():
            job = extract_job_number(str(row['Customer']))
            if job:
                # Expenses are typically negative in QuickBooks, so we take absolute value
                expense = abs(row['Expenses']) if pd.notna(row['Expenses']) else 0
                if job in costs:
                    costs[job] += expense
                else:
                    costs[job] = expense
    
    return costs

def process_estimate_values(filename):
    """Process estimate values from DASH"""
    df = pd.read_excel(filename, engine='openpyxl')
    
    # The file has Job Number in first column and Total Estimates in second column
    estimates = {}
    
    if len(df.columns) >= 2:
        # Assuming first row is header
        for _, row in df.iterrows():
            job = str(row.iloc[0]) if pd.notna(row.iloc[0]) else None
            estimate = row.iloc[1] if pd.notna(row.iloc[1]) else 0
            
            if job and job != 'Job Number' and estimate != 'Total Estimates':
                # Clean job number - it's already in correct format
                if '-' in job and len(job.split('-')) == 3:
                    try:
                        estimates[job] = float(estimate)
                    except (ValueError, TypeError):
                        pass
    
    return estimates

def process_prior_wip(filename):
    """Process prior month WIP to get list of jobs"""
    df = pd.read_excel(filename, engine='openpyxl')
    
    # The file has Job Number in first column (same structure as estimate values)
    jobs = []
    
    if len(df.columns) >= 1:
        for _, row in df.iterrows():
            job = str(row.iloc[0]) if pd.notna(row.iloc[0]) else None
            
            if job and job != 'Job Number' and '-' in job and len(job.split('-')) == 3:
                jobs.append(job)
    
    return jobs

def get_division_from_job(job_number):
    """Extract division from job number (last 3 letters)"""
    if job_number and len(job_number) >= 3:
        # Check if it's a valid job number format (XX-XXXX-XXX)
        if '-' in job_number and len(job_number.split('-')) == 3:
            return job_number[-3:].upper()
    return 'REP'  # Default division

def calculate_wip():
    """Main function to calculate WIP"""
    
    print("Starting WIP calculation for June (as of 5/31)...")
    
    # 1. Read all data files
    print("\n1. Reading data files...")
    
    # Update file paths to include the subfolder
    folder_path = 'WIP 6-1-25/'
    
    try:
        production_jobs = process_production_list(folder_path + 'May Production List.xlsx')
        print(f"   Found {len(production_jobs)} jobs in production list")
    except Exception as e:
        print(f"   ERROR reading production list: {e}")
        production_jobs = []
    
    try:
        invoiced_jobs = process_invoice_list(folder_path + 'May Invoiced List.xlsx')
        print(f"   Found {len(invoiced_jobs)} jobs in invoice list")
    except Exception as e:
        print(f"   ERROR reading invoice list: {e}")
        invoiced_jobs = []
    
    try:
        invoiced_amounts = process_invoiced_amounts(folder_path + 'Invoiced Amount Data.xlsx')
        print(f"   Found invoiced amounts for {len(invoiced_amounts)} jobs")
    except Exception as e:
        print(f"   ERROR reading invoiced amounts: {e}")
        invoiced_amounts = {}
    
    try:
        actual_costs = process_actual_costs(folder_path + 'Actual Cost Data.xlsx')
        print(f"   Found actual costs for {len(actual_costs)} jobs")
    except Exception as e:
        print(f"   ERROR reading actual costs: {e}")
        actual_costs = {}
    
    try:
        estimate_values = process_estimate_values(folder_path + 'Estimate Values Data.xlsx')
        print(f"   Found estimates for {len(estimate_values)} jobs")
    except Exception as e:
        print(f"   ERROR reading estimate values: {e}")
        estimate_values = {}
    
    try:
        prior_wip_jobs = process_prior_wip(folder_path + 'Prior Month WIP.xlsx')
        print(f"   Found {len(prior_wip_jobs)} jobs in prior month WIP")
    except Exception as e:
        print(f"   ERROR reading prior WIP: {e}")
        prior_wip_jobs = []
    
    # 2. Create Current WIP List
    print("\n2. Creating Current WIP List...")
    
    # Start with production jobs and add prior WIP
    possible_wip = list(set(production_jobs + prior_wip_jobs))
    print(f"   Possible WIP jobs: {len(possible_wip)}")
    
    # Remove fully invoiced jobs
    current_wip_list = []
    partially_invoiced = []
    
    for job in possible_wip:
        # Check if job was invoiced this month
        if job in invoiced_jobs:
            # Check if it's a partial invoice
            est_amount = estimate_values.get(job, 0)
            inv_amount = invoiced_amounts.get(job, 0)
            
            # If invoiced amount is less than estimate, it's a partial invoice
            if inv_amount < est_amount * 0.95:  # 95% threshold for rounding
                current_wip_list.append(job)
                partially_invoiced.append(job)
        else:
            # Not invoiced this month, keep in WIP
            current_wip_list.append(job)
    
    print(f"   Current WIP List: {len(current_wip_list)} jobs")
    print(f"   Partially invoiced jobs retained: {len(partially_invoiced)}")
    
    # 3. Calculate WIP values
    print("\n3. Calculating WIP values...")
    
    wip_data = []
    total_wip_accrual = 0
    total_old_calc = 0
    
    for job in sorted(current_wip_list):
        division = get_division_from_job(job)
        
        # Skip warranty jobs
        if division in ['WTY', 'WARRANTY']:
            continue
        
        estimate = estimate_values.get(job, 0)
        invoiced = invoiced_amounts.get(job, 0)
        actual_cost = abs(actual_costs.get(job, 0))  # Use absolute value
        
        # Calculate expected cost based on division
        cost_percentage = DIVISION_COST_PERCENTAGES.get(division, 0.48)  # Default to REP
        expected_cost = estimate * cost_percentage
        expected_profit = estimate - expected_cost
        
        # Calculate percentage of completion
        if expected_cost > 0:
            pct_complete = min(actual_cost / expected_cost, 1.0)
        else:
            pct_complete = 0
        
        # Calculate WIP accrual (PoC method)
        poc_value = max((estimate * pct_complete) - invoiced, 0)
        
        # Old calculation method (for reference)
        if pct_complete < 0.9:
            old_calc = (estimate - invoiced) * pct_complete
        else:
            old_calc = 0.9 * (estimate - invoiced)
        
        wip_data.append({
            'Status': 'WIP',
            'Job #': job,
            'Div': division,
            'Estimate Value': estimate,
            'Invoiced': invoiced,
            'Actual Cost': actual_cost,
            'Expected Cost': expected_cost,
            'Expected Profit': expected_profit,
            'Estimated % of Completion': pct_complete,
            'PoC Value': poc_value,
            'Old Calc Method': old_calc
        })
        
        total_wip_accrual += poc_value
        total_old_calc += old_calc
    
    print(f"   Total WIP Accrual: ${total_wip_accrual:,.2f}")
    print(f"   Total Old Calc: ${total_old_calc:,.2f}")
    
    # 4. Create output workbook
    print("\n4. Creating output workbook...")

    # openpyxl imports removed as they're not used in this version

    # Generate timestamp for unique filenames
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    main_filename = f'JuneWIP_531_{timestamp}.xlsx'
    clean_filename = f'JuneWIP_531_CLEAN_{timestamp}.xlsx'

    with pd.ExcelWriter(main_filename, engine='openpyxl') as writer:
        # Sheet 1: Estimate Data
        est_df = pd.DataFrame([(k, v) for k, v in estimate_values.items()], 
                            columns=['Job Number', 'Total Estimates'])
        est_df.to_excel(writer, sheet_name='Estimate Data', index=False)
        
        # Sheet 2: Invoicing Data
        inv_df = pd.DataFrame([(k, v) for k, v in invoiced_amounts.items()], 
                            columns=['Job Number', 'Total Invoiced'])
        inv_df.to_excel(writer, sheet_name='Invoicing Data', index=False)
        
        # Sheet 3: Cost Data
        cost_df = pd.DataFrame([(k, v) for k, v in actual_costs.items()], 
                             columns=['Job Number', 'Total Cost'])
        cost_df.to_excel(writer, sheet_name='Cost Data', index=False)
        
        # Sheet 4: May Production List
        prod_df = pd.DataFrame(production_jobs, columns=['Job Number'])
        prod_df.to_excel(writer, sheet_name='May Production List', index=False)
        
        # Sheet 5: Possible WIP
        poss_df = pd.DataFrame(possible_wip, columns=['Job Number'])
        poss_df.to_excel(writer, sheet_name='Possible WIP', index=False)
        
        # Sheet 6: May Invoice
        inv_list_df = pd.DataFrame(invoiced_jobs, columns=['Job Number'])
        inv_list_df.to_excel(writer, sheet_name='May Invoice', index=False)
        
        # Sheet 7: Current WIP List
        curr_df = pd.DataFrame(current_wip_list, columns=['Job Number'])
        curr_df.to_excel(writer, sheet_name='Current WIP List', index=False)
        
        # Sheet 8: Final WIP
        final_df = pd.DataFrame(wip_data)
        # Add totals row
        totals_row = pd.Series({
            'Status': 'TOTAL',
            'Job #': '',
            'Div': '',
            'Estimate Value': final_df['Estimate Value'].sum(),
            'Invoiced': final_df['Invoiced'].sum(),
            'Actual Cost': final_df['Actual Cost'].sum(),
            'Expected Cost': final_df['Expected Cost'].sum(),
            'Expected Profit': final_df['Expected Profit'].sum(),
            'Estimated % of Completion': '',
            'PoC Value': final_df['PoC Value'].sum(),
            'Old Calc Method': final_df['Old Calc Method'].sum()
        })
        final_df = pd.concat([final_df, totals_row.to_frame().T], ignore_index=True)
        
        final_df.to_excel(writer, sheet_name='Final WIP', index=False)
        
    print(f"\nWIP calculation complete! Output saved to '{main_filename}'")

    # 5. Create cleaned WIP workbook (remove $0 PoC and negative values)
    print("\n5. Creating cleaned WIP workbook...")

    # Filter out jobs with $0 PoC value or negative values
    # BUT keep MIT and WTR jobs even if they have $0 PoC (they may need manual estimates)
    cleaned_wip_data = []
    for job_data in wip_data:
        poc_value = job_data['PoC Value']
        estimate_value = job_data['Estimate Value']
        division = job_data['Div']

        # Keep jobs that have positive PoC value and non-negative estimate values
        # OR keep MIT/WTR jobs even with $0 PoC (they may need manual estimates)
        if (poc_value > 0 and estimate_value >= 0) or (division in ['MIT', 'WTR'] and estimate_value >= 0):
            cleaned_wip_data.append(job_data)

    print(f"   Filtered from {len(wip_data)} to {len(cleaned_wip_data)} jobs")
    print(f"   Removed {len(wip_data) - len(cleaned_wip_data)} jobs with $0 PoC or negative values")
    print(f"   (MIT and WTR jobs with $0 PoC are retained for manual estimate input)")

    # Calculate cleaned totals
    cleaned_total_wip = sum(job['PoC Value'] for job in cleaned_wip_data)
    cleaned_total_estimate = sum(job['Estimate Value'] for job in cleaned_wip_data)
    cleaned_total_invoiced = sum(job['Invoiced'] for job in cleaned_wip_data)
    cleaned_total_cost = sum(job['Actual Cost'] for job in cleaned_wip_data)
    cleaned_total_expected_cost = sum(job['Expected Cost'] for job in cleaned_wip_data)
    cleaned_total_expected_profit = sum(job['Expected Profit'] for job in cleaned_wip_data)
    cleaned_total_old_calc = sum(job['Old Calc Method'] for job in cleaned_wip_data)

    # Create cleaned workbook
    if cleaned_wip_data:
        cleaned_df = pd.DataFrame(cleaned_wip_data)

        # Add totals row for cleaned data
        cleaned_totals_row = pd.Series({
            'Status': 'TOTAL',
            'Job #': '',
            'Div': '',
            'Estimate Value': cleaned_total_estimate,
            'Invoiced': cleaned_total_invoiced,
            'Actual Cost': cleaned_total_cost,
            'Expected Cost': cleaned_total_expected_cost,
            'Expected Profit': cleaned_total_expected_profit,
            'Estimated % of Completion': '',
            'PoC Value': cleaned_total_wip,
            'Old Calc Method': cleaned_total_old_calc
        })
        cleaned_df = pd.concat([cleaned_df, cleaned_totals_row.to_frame().T], ignore_index=True)

        # Save cleaned workbook
        with pd.ExcelWriter(clean_filename, engine='openpyxl') as writer:
            cleaned_df.to_excel(writer, sheet_name='Clean WIP', index=False)

            # Also add summary sheets for reference
            if len(cleaned_wip_data) > 0:
                # Division summary
                div_summary = pd.DataFrame(cleaned_wip_data).groupby('Div').agg({
                    'Job #': 'count',
                    'PoC Value': 'sum',
                    'Estimate Value': 'sum'
                }).round(2)
                div_summary.columns = ['Job Count', 'Total PoC Value', 'Total Estimate Value']
                div_summary.to_excel(writer, sheet_name='Division Summary')

                # Top jobs
                top_jobs = pd.DataFrame(cleaned_wip_data).nlargest(20, 'PoC Value')[['Job #', 'Div', 'PoC Value', 'Estimate Value', 'Estimated % of Completion']]
                top_jobs.to_excel(writer, sheet_name='Top Jobs', index=False)

        print(f"   Clean WIP workbook saved to '{clean_filename}'")
        print(f"   Clean WIP Total: ${cleaned_total_wip:,.2f}")
    else:
        print("   No jobs with positive PoC values found for clean workbook")

    print(f"\nSummary:")
    print(f"  Total jobs in WIP: {len(wip_data)}")
    print(f"  Total WIP Accrual: ${total_wip_accrual:,.2f}")
    print(f"  Clean jobs in WIP: {len(cleaned_wip_data)}")
    print(f"  Clean WIP Accrual: ${cleaned_total_wip:,.2f}")

    # Print division breakdown
    if wip_data:
        division_summary = pd.DataFrame(wip_data).groupby('Div')['PoC Value'].agg(['count', 'sum'])
        division_summary.columns = ['Job Count', 'Total PoC Value']
        division_summary['Total PoC Value'] = division_summary['Total PoC Value'].apply(lambda x: f'${x:,.2f}')
        print(f"\nBreakdown by Division (All WIP):")
        print(division_summary)

        # Show jobs with highest WIP values
        top_wip = pd.DataFrame(wip_data).nlargest(10, 'PoC Value')[['Job #', 'Div', 'PoC Value']]
        top_wip['PoC Value'] = top_wip['PoC Value'].apply(lambda x: f'${x:,.2f}')
        print(f"\nTop 10 WIP Jobs (All):")
        print(top_wip)

        # Show cleaned division breakdown
        if cleaned_wip_data:
            cleaned_division_summary = pd.DataFrame(cleaned_wip_data).groupby('Div')['PoC Value'].agg(['count', 'sum'])
            cleaned_division_summary.columns = ['Job Count', 'Total PoC Value']
            cleaned_division_summary['Total PoC Value'] = cleaned_division_summary['Total PoC Value'].apply(lambda x: f'${x:,.2f}')
            print(f"\nBreakdown by Division (Clean WIP):")
            print(cleaned_division_summary)

if __name__ == "__main__":
    calculate_wip()
