# Data Folder

This folder should contain your QuickBooks export files for processing.

## Required Files Structure

Create a subfolder for each month's data (e.g., `WIP 6-1-25/`) containing:

```
data/
└── WIP 6-1-25/
    ├── May Production List.xlsx
    ├── May Invoiced List.xlsx
    ├── Invoiced Amount Data.xlsx
    ├── Actual Cost Data.xlsx
    ├── Estimate Values Data.xlsx
    └── Prior Month WIP.xlsx
```

## File Descriptions

- **May Production List.xlsx**: QuickBooks "Income by Customer Summary" report for jobs with expenses in May
- **May Invoiced List.xlsx**: QuickBooks "Invoice List by Date" report for May invoices
- **Invoiced Amount Data.xlsx**: QuickBooks "Income by Customer Summary" report for all invoiced amounts
- **Actual Cost Data.xlsx**: QuickBooks "Income by Customer Summary" report showing actual costs
- **Estimate Values Data.xlsx**: DASH export with job numbers and total estimates
- **Prior Month WIP.xlsx**: Previous month's WIP job list

## Security Note

⚠️ **Important**: Data files are excluded from Git for security reasons. Never commit actual financial data to version control.

## Setup Instructions

1. Create the monthly folder (e.g., `WIP 6-1-25/`)
2. Export the required reports from QuickBooks
3. Place files in the folder with exact names as listed above
4. Update the folder path in `fixed-wip-automation-script.py` if needed
5. Run the automation script
