# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
PIPFILE.lock

# Virtual Environment
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Excel files (output)
*.xlsx
!template*.xlsx
!sample*.xlsx

# Logs
*.log
error.log

# Temporary files
*.tmp
*.temp
~$*

# Data files (sensitive)
WIP*/
*WIP*/
data/
Data/

# Backup files
*.bak
*.backup

# Test outputs
test_output/
temp_test/
