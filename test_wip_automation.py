#!/usr/bin/env python3
"""
Comprehensive test suite for the WIP automation script.
Tests all functions and validates the output.
"""

import pandas as pd
import numpy as np
import os
import sys
from datetime import datetime
import unittest
from unittest.mock import patch, MagicMock

# Import the functions from the main script
sys.path.append('.')
import importlib.util
spec = importlib.util.spec_from_file_location("wip_script", "fixed-wip-automation-script.py")
wip_script = importlib.util.module_from_spec(spec)
spec.loader.exec_module(wip_script)

# Import functions
read_qb_report = wip_script.read_qb_report
extract_job_number = wip_script.extract_job_number
process_production_list = wip_script.process_production_list
process_invoice_list = wip_script.process_invoice_list
process_invoiced_amounts = wip_script.process_invoiced_amounts
process_actual_costs = wip_script.process_actual_costs
process_estimate_values = wip_script.process_estimate_values
process_prior_wip = wip_script.process_prior_wip
get_division_from_job = wip_script.get_division_from_job
DIVISION_COST_PERCENTAGES = wip_script.DIVISION_COST_PERCENTAGES

class TestWIPAutomation(unittest.TestCase):
    
    def setUp(self):
        """Set up test fixtures"""
        self.test_folder = 'WIP 6-1-25/'
        
    def test_extract_job_number(self):
        """Test job number extraction function"""
        print("\n=== Testing job number extraction ===")
        
        # Test valid job numbers
        test_cases = [
            ("24-0259-REP", "24-0259-REP"),
            ("Customer: 25-0281-TMP", "25-0281-TMP"),
            ("Some text 23-1234-CON more text", "23-1234-CON"),
            ("No job number here", None),
            ("", None),
            (None, None),
            ("24-0259-REP;PRAH-0021936", "24-0259-REP")
        ]
        
        for input_str, expected in test_cases:
            result = extract_job_number(input_str)
            print(f"  Input: '{input_str}' -> Output: '{result}' (Expected: '{expected}')")
            self.assertEqual(result, expected)
    
    def test_get_division_from_job(self):
        """Test division extraction from job number"""
        print("\n=== Testing division extraction ===")
        
        test_cases = [
            ("24-0259-REP", "REP"),
            ("25-0281-TMP", "TMP"),
            ("23-1234-CON", "CON"),
            ("invalid", "REP"),  # Default
            ("", "REP"),  # Default
            (None, "REP")  # Default
        ]
        
        for job_number, expected in test_cases:
            result = get_division_from_job(job_number)
            print(f"  Job: '{job_number}' -> Division: '{result}' (Expected: '{expected}')")
            self.assertEqual(result, expected)
    
    def test_division_cost_percentages(self):
        """Test that all division cost percentages are valid"""
        print("\n=== Testing division cost percentages ===")
        
        for division, percentage in DIVISION_COST_PERCENTAGES.items():
            print(f"  {division}: {percentage:.2%}")
            self.assertIsInstance(percentage, (int, float))
            self.assertGreaterEqual(percentage, 0)
            self.assertLessEqual(percentage, 1)
    
    def test_file_existence(self):
        """Test that all required data files exist"""
        print("\n=== Testing file existence ===")
        
        required_files = [
            'May Production List.xlsx',
            'May Invoiced List.xlsx',
            'Invoiced Amount Data.xlsx',
            'Actual Cost Data.xlsx',
            'Estimate Values Data.xlsx',
            'Prior Month WIP.xlsx'
        ]
        
        for filename in required_files:
            filepath = os.path.join(self.test_folder, filename)
            exists = os.path.exists(filepath)
            print(f"  {filename}: {'✓' if exists else '✗'}")
            self.assertTrue(exists, f"Required file {filename} not found")
    
    def test_data_file_reading(self):
        """Test reading of actual data files"""
        print("\n=== Testing data file reading ===")
        
        # Test production list
        try:
            production_jobs = process_production_list(self.test_folder + 'May Production List.xlsx')
            print(f"  Production jobs: {len(production_jobs)} found")
            self.assertIsInstance(production_jobs, list)
            self.assertGreater(len(production_jobs), 0)
            
            # Check that job numbers are in correct format
            for job in production_jobs[:5]:  # Check first 5
                self.assertRegex(job, r'\d{2}-\d{4}-[A-Z]{3}')
        except Exception as e:
            self.fail(f"Failed to process production list: {e}")
        
        # Test estimate values
        try:
            estimates = process_estimate_values(self.test_folder + 'Estimate Values Data.xlsx')
            print(f"  Estimate values: {len(estimates)} found")
            self.assertIsInstance(estimates, dict)
            self.assertGreater(len(estimates), 0)
            
            # Check that values are numeric
            for job, value in list(estimates.items())[:5]:
                self.assertIsInstance(value, (int, float))
                self.assertGreaterEqual(value, 0)
        except Exception as e:
            self.fail(f"Failed to process estimate values: {e}")
        
        # Test invoiced amounts
        try:
            invoiced = process_invoiced_amounts(self.test_folder + 'Invoiced Amount Data.xlsx')
            print(f"  Invoiced amounts: {len(invoiced)} found")
            self.assertIsInstance(invoiced, dict)
        except Exception as e:
            self.fail(f"Failed to process invoiced amounts: {e}")
        
        # Test actual costs
        try:
            costs = process_actual_costs(self.test_folder + 'Actual Cost Data.xlsx')
            print(f"  Actual costs: {len(costs)} found")
            self.assertIsInstance(costs, dict)
        except Exception as e:
            self.fail(f"Failed to process actual costs: {e}")
    
    def test_output_file_generation(self):
        """Test that output file is generated correctly"""
        print("\n=== Testing output file generation ===")

        # Look for timestamped output files
        output_files = [f for f in os.listdir('.') if f.startswith('JuneWIP_531_') and f.endswith('.xlsx') and 'CLEAN' not in f]
        clean_files = [f for f in os.listdir('.') if f.startswith('JuneWIP_531_CLEAN_') and f.endswith('.xlsx')]

        # Check main output file
        if output_files:
            output_file = max(output_files)  # Get most recent
            print(f"  Main output file exists: ✓ ({output_file})")

            # Read and validate the output file
            try:
                # Check each sheet
                expected_sheets = [
                    'Estimate Data', 'Invoicing Data', 'Cost Data',
                    'May Production List', 'Possible WIP', 'May Invoice',
                    'Current WIP List', 'Final WIP'
                ]

                with pd.ExcelFile(output_file) as xls:
                    actual_sheets = xls.sheet_names
                    print(f"  Sheets found: {actual_sheets}")

                    for sheet in expected_sheets:
                        self.assertIn(sheet, actual_sheets, f"Missing sheet: {sheet}")

                    # Check Final WIP sheet structure
                    final_wip = pd.read_excel(output_file, sheet_name='Final WIP')
                    print(f"  Final WIP shape: {final_wip.shape}")

                    expected_columns = [
                        'Status', 'Job #', 'Div', 'Estimate Value', 'Invoiced',
                        'Actual Cost', 'Expected Cost', 'Expected Profit',
                        'Estimated % of Completion', 'PoC Value', 'Old Calc Method'
                    ]

                    for col in expected_columns:
                        self.assertIn(col, final_wip.columns, f"Missing column: {col}")

                    # Check that we have data
                    self.assertGreater(len(final_wip), 0, "Final WIP sheet is empty")

                    # Check for totals row
                    total_rows = final_wip[final_wip['Status'] == 'TOTAL']
                    self.assertEqual(len(total_rows), 1, "Should have exactly one TOTAL row")

                    print(f"  Main WIP validation: ✓")

            except Exception as e:
                self.fail(f"Failed to validate main output file: {e}")
        else:
            self.fail("Main output file was not generated")

        # Check clean output file
        if clean_files:
            clean_file = max(clean_files)  # Get most recent
            print(f"  Clean output file exists: ✓ ({clean_file})")

            try:
                # Check clean file sheets
                expected_clean_sheets = ['Clean WIP', 'Division Summary', 'Top Jobs']

                with pd.ExcelFile(clean_file) as xls:
                    actual_sheets = xls.sheet_names
                    print(f"  Clean sheets found: {actual_sheets}")

                    for sheet in expected_clean_sheets:
                        self.assertIn(sheet, actual_sheets, f"Missing clean sheet: {sheet}")

                    # Check Clean WIP sheet
                    clean_wip = pd.read_excel(clean_file, sheet_name='Clean WIP')
                    print(f"  Clean WIP shape: {clean_wip.shape}")

                    # Remove totals row for validation
                    data_rows = clean_wip[clean_wip['Status'] != 'TOTAL']

                    # Validate filtering logic: no zero PoC values except for MIT/WTR jobs
                    zero_poc = data_rows[data_rows['PoC Value'] <= 0]
                    negative_estimates = data_rows[data_rows['Estimate Value'] < 0]

                    # Check that zero PoC jobs are only MIT or WTR
                    if len(zero_poc) > 0:
                        non_mit_wtr_zero = zero_poc[~zero_poc['Div'].isin(['MIT', 'WTR'])]
                        self.assertEqual(len(non_mit_wtr_zero), 0, "Only MIT/WTR jobs should have zero PoC values in clean WIP")

                    self.assertEqual(len(negative_estimates), 0, "Clean WIP should have no negative estimates")

                    print(f"  Clean WIP validation: ✓")
                    print(f"  Clean jobs count: {len(data_rows)}")

            except Exception as e:
                self.fail(f"Failed to validate clean output file: {e}")
        else:
            self.fail("Clean output file was not generated")

def run_performance_test():
    """Run a performance test of the main script"""
    print("\n" + "="*50)
    print("PERFORMANCE TEST")
    print("="*50)
    
    import time
    start_time = time.time()
    
    # Import and run the main function
    calculate_wip = wip_script.calculate_wip
    calculate_wip()
    
    end_time = time.time()
    execution_time = end_time - start_time
    
    print(f"\nExecution time: {execution_time:.2f} seconds")
    
    if execution_time < 30:
        print("Performance: ✓ GOOD (< 30 seconds)")
    elif execution_time < 60:
        print("Performance: ⚠ ACCEPTABLE (30-60 seconds)")
    else:
        print("Performance: ✗ SLOW (> 60 seconds)")

def run_data_validation():
    """Run data validation checks"""
    print("\n" + "="*50)
    print("DATA VALIDATION")
    print("="*50)
    
    output_file = 'JuneWIP_531.xlsx'
    
    if not os.path.exists(output_file):
        print("❌ Output file not found. Run the main script first.")
        return
    
    try:
        final_wip = pd.read_excel(output_file, sheet_name='Final WIP')
        
        # Remove totals row for analysis
        data_rows = final_wip[final_wip['Status'] != 'TOTAL']
        totals_row = final_wip[final_wip['Status'] == 'TOTAL'].iloc[0]
        
        print(f"Total jobs in WIP: {len(data_rows)}")
        print(f"Total WIP Accrual: ${totals_row['PoC Value']:,.2f}")
        
        # Check for data quality issues
        issues = []
        
        # Check for negative values where they shouldn't be
        if (data_rows['Estimate Value'] < 0).any():
            issues.append("Negative estimate values found")
        
        if (data_rows['PoC Value'] < 0).any():
            issues.append("Negative PoC values found")
        
        # Check for unrealistic completion percentages
        high_completion = data_rows[data_rows['Estimated % of Completion'] > 1.5]
        if len(high_completion) > 0:
            issues.append(f"{len(high_completion)} jobs with >150% completion")
        
        # Check for jobs with no estimate value
        no_estimate = data_rows[data_rows['Estimate Value'] == 0]
        if len(no_estimate) > 0:
            issues.append(f"{len(no_estimate)} jobs with no estimate value")
        
        if issues:
            print("\n⚠ Data Quality Issues:")
            for issue in issues:
                print(f"  - {issue}")
        else:
            print("\n✓ No data quality issues found")
        
        # Show summary statistics
        print(f"\nSummary Statistics:")
        print(f"  Average estimate value: ${data_rows['Estimate Value'].mean():,.2f}")
        print(f"  Average PoC value: ${data_rows['PoC Value'].mean():,.2f}")
        print(f"  Average completion %: {data_rows['Estimated % of Completion'].mean():.1%}")
        
    except Exception as e:
        print(f"❌ Error during data validation: {e}")

if __name__ == "__main__":
    print("WIP AUTOMATION TESTING SUITE")
    print("="*50)
    
    # Run unit tests
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # Run performance test
    run_performance_test()
    
    # Run data validation
    run_data_validation()
    
    print("\n" + "="*50)
    print("TESTING COMPLETE")
    print("="*50)
