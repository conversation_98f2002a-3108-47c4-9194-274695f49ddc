# Changelog

All notable changes to the WIP Automation project will be documented in this file.

## [1.3.2] - 2025-06-12

### Fixed
- **WTR Jobs Filtering**: Improved filtering logic for WTR jobs to reduce noise in WIP reports
- WTR jobs now only included if they have positive estimates, significant actual costs (>$500), or recent activity
- Reduced WTR jobs from 335 to 297 (38 stale jobs filtered out)
- Total WIP jobs reduced from 1,415 to 1,377 while maintaining same WIP accrual value

### Technical Details
- Added enhanced filtering in step 2 of WIP calculation for WTR division jobs
- Filters out WTR jobs with zero estimates AND minimal costs (<$500) unless in current production
- Maintains all jobs with meaningful financial activity or recent work

## [1.3.1] - 2025-06-12

### Fixed
- **Smart Clean WIP Filtering**: MIT and WTR jobs with $0 PoC values are now retained in clean workbook
- These jobs may legitimately have $0 PoC while waiting for manual estimate input
- Clean workbook now shows 542 jobs (up from 233) including MIT/WTR jobs needing attention

### Technical Details
- Clean workbook now filters out 873 jobs (down from 1,182) with $0 PoC values
- Retains all 10 MIT jobs and 335 WTR jobs for manual estimate review
- 309 of the retained jobs have $0 PoC and may need manual estimates

## [1.3.0] - 2025-06-11

### Added
- **Clean WIP Workbook**: New additional output file that removes jobs with $0 PoC values and negative estimates
- Clean workbook includes three sheets: Clean WIP, Division Summary, and Top Jobs
- Timestamped filenames to prevent file conflicts when Excel files are open
- Enhanced validation script to test both main and clean workbooks
- Updated test suite to validate clean workbook functionality

### Changed
- Output files now include timestamps in filenames for better organization
- Improved summary reporting to show both total and clean job counts
- Enhanced documentation to reflect new clean workbook feature

### Technical Details
- Clean workbook provides focused view of meaningful WIP jobs totaling $1,199,154.85
- Maintains all original functionality while adding new clean output option

## [1.2.0] - 2025-06-11

### Added
- Comprehensive test suite (`test_wip_automation.py`)
- Output validation tool (`validate_output.py`)
- Improved debug output for file processing
- Git repository setup with proper structure
- Documentation (README.md, CHANGELOG.md)

### Fixed
- QuickBooks file header detection and parsing
- Division extraction function for invalid job numbers
- Column name handling for Excel files with unnamed columns

### Changed
- Enhanced error handling and logging
- Improved job number extraction regex
- Better data quality validation

## [1.1.0] - 2025-06-11

### Added
- Debug output for troubleshooting file reading issues
- Better error messages for missing columns
- Manual column name setting for problematic Excel files

### Fixed
- Excel file reading when headers are not properly detected
- Column name processing for QuickBooks exports

## [1.0.0] - 2025-06-11

### Added
- Initial working version of WIP automation script
- Percentage of Completion (PoC) calculation method
- Division-based cost percentage calculations
- Support for multiple QuickBooks export formats
- Excel output with 8 comprehensive sheets
- Job number extraction and validation
- Prior month WIP integration

### Features
- Processes 6 different input file types
- Calculates WIP values using industry-standard PoC method
- Generates detailed Excel reports
- Handles partial invoicing scenarios
- Division breakdown and analysis
- Top jobs reporting

### Technical Details
- Python 3.x compatible
- Uses pandas for data processing
- openpyxl for Excel file handling
- Regex-based job number extraction
- Configurable division cost percentages
